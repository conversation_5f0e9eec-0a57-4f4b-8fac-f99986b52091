# -*- coding: utf-8 -*-
"""
Dashboard Views Module

This module contains API views for dashboard-related functionality.
"""
import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiExample, extend_schema_view

from .services import DashboardStatsService
from .serializers import (
    DashboardOverviewResponseSerializer,
    DashboardParameterSerializer,
    TcdNewsSerializer,
    TcdDashboardCategorySerializer,
    TcdDashboardSerializer,
    TcdDashboardCategorySerializer,
    DashboardListParameterSerializer,
)
from utils.response import APIResponse, get_language_from_request
from MCDC.models import TcdNews
from utils.pagination import CustomPagination

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["Dashboard"],
    summary="Get dashboard overview statistics",
    description="Returns comprehensive overview statistics including projects, members, consultants, and engagement metrics",
    request=DashboardParameterSerializer,
    responses={
        200: DashboardOverviewResponseSerializer,
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    examples=[
        OpenApiExample(
            'Dashboard Overview Request',
            summary='Get dashboard overview statistics',
            description='Returns comprehensive overview statistics',
            value={
                "language": "th"
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([])
def dashboard_overview(request):
    """
    Get dashboard overview statistics
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = DashboardParameterSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get overview statistics
        result = DashboardStatsService.get_overview_stats(
            language=language,
            project_size=serializer.validated_data.get('project_size', 3),
            news_size=serializer.validated_data.get('news_size', 3)
        )
        
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language,
                status_code=status.HTTP_200_OK
            )
        else:
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    except Exception as e:
        logger.error(f"Error in dashboard overview: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["News"],
    responses={
        200: TcdNewsSerializer(many=True),
    }
)
class NewsViewSet(ReadOnlyModelViewSet):
    """
    ViewSet for dashboard analytics and statistics
    
    Provides comprehensive dashboard functionality including:
    - Overview statistics
    - Project analytics
    - Consultant analytics
    - Member analytics
    - System health metrics
    - Search analytics
    """
    
    queryset = TcdNews.objects.all()
    serializer_class = TcdNewsSerializer
    permission_classes = [AllowAny]
    pagination_class = CustomPagination
    
    def list(self, request, *args, **kwargs):
        queryset = TcdNews.objects.filter(status=1)
        pagination = self.paginate_queryset(queryset)
        serializer = self.get_serializer(pagination, many=True)
        if pagination:
            return self.get_paginated_response(serializer.data)
        else:
            return Response(serializer.data, status=status.HTTP_200_OK)
        
    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return APIResponse.success(
                data=serializer.data,
                language=get_language_from_request(request),
                    status_code=status.HTTP_200_OK
                )
        except Exception as e:
            logger.error(f"Error in news retrieve: {str(e)}")
            return APIResponse.error(
                error_code=5000,
                language=get_language_from_request(request),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    tags=["Dashboard Category"],
    responses={
        200: TcdDashboardCategorySerializer(many=True),
    }
)
@api_view(['GET'])
@permission_classes([AllowAny])
def dashboard_category(request):
    """
    Get dashboard category
    """
    language = get_language_from_request(request)

    try:
        result = DashboardStatsService.get_dashboard_category(language)
        return APIResponse.success(
            data=result['data'],
            language=language,
            status_code=status.HTTP_200_OK
        )

    except Exception as e:
        logger.error(f"Error in dashboard category: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Dashboard"],
    request=DashboardListParameterSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": True},
                "error_code": {"type": "integer", "example": None},
                "error_message": {"type": "string", "example": None},
                "data": TcdDashboardSerializer(many=True),
                "page": {"type": "integer", "example": 1},
                "per_page": {"type": "integer", "example": 10},
                "total": {"type": "integer", "example": 25},
                "has_next": {"type": "boolean", "example": True},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
    }
)
@api_view(['POST'])
@permission_classes([AllowAny])
def dashboard_list(request):
    """
    Get dashboard list with pagination
    """
    language = get_language_from_request(request)
    
    try:
        # Get pagination parameters from query string
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        
        # Validate pagination parameters
        if page < 1:
            page = 1
        if page_size < 1:
            page_size = 10
        if page_size > 100:  # Limit maximum page size
            page_size = 100
            
        dashboard_category_id = request.data.get('dashboard_category_id', None)

        result = DashboardStatsService.get_dashboard_list(
            dashboard_category_id=dashboard_category_id, 
            language=language,
            page=page,
            page_size=page_size
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except ValueError as e:
        logger.error(f"Invalid pagination parameters: {str(e)}")
        return APIResponse.error(
            error_code=4000,
            language=language,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Error in dashboard list: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
